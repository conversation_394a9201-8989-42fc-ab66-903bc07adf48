["HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_configuration_structure", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_content_processor_functionality", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_data_integrator_functionality", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_documentation_completeness", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_error_handling_robustness", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_final_acceptance_summary", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_hybrid_task_executor_functionality", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_information_analyzer_functionality", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_performance_benchmarks", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_phase4_component_availability", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_prompt_templates_completeness", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_search_operations_functionality", "HyAIAgent/test/test_phase4_acceptance.py::TestPhase4Acceptance::test_test_coverage_completeness", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_concurrent_hybrid_tasks", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_end_to_end_performance", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_error_recovery_integration", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_hybrid_task_research_workflow", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_multi_source_data_integration", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_processing_to_analysis_workflow", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_prompt_template_integration", "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_search_to_processing_pipeline", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_configuration_validation", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_content_summary_template_structure", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_file_operations_integration", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_information_analysis_template_structure", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_integration_workflow_simulation", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_prompt_templates_exist_and_valid", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_research_planning_template_structure", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_template_code_block_syntax", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_template_content_quality", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_template_markdown_structure", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_template_variable_consistency", "HyAIAgent/test/test_phase4_simple_integration.py::TestPhase4SimpleIntegration::test_web_search_template_structure", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_content_summary_template_functionality", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_content_summary_template_structure", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_information_analysis_template_functionality", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_information_analysis_template_structure", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_prompt_files_exist", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_research_planning_template_functionality", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_research_planning_template_structure", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_template_code_blocks", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_template_completeness", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_template_encoding", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_template_file_sizes", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_template_markdown_headers", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_template_variable_format", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_web_search_template_functionality", "HyAIAgent/test/test_prompt_templates.py::TestPromptTemplates::test_web_search_template_structure"]